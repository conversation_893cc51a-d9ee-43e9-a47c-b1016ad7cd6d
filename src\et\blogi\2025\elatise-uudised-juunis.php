<!-- LINK ‧ ASENDA
    Postituse pealkiri (Sentence case): Elatise uudised juunis
    Postituse ja Facebooki kirjeld<PERSON> (Sentence case): POSTITUSEKIRJELDUS
    Postituse link (lower-case, /link.php): elatise-uudised-juuni<PERSON> kuup<PERSON>ev (dd.mm.yyyy): 30.05.2025
    Postituse kuupäev (yyyy-mm-dd): 2025-06-30
    Postituse seo märksõnad: MARKSONAD
    -->



<?php
?>
<!DOCTYPE html>
<html lang="et" class="relative min-h-full light">

<head>
    <!-- LINK ‧ Meta -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Ülevaade juunis 2025 avaldatud elatise ja peretoetustega seotud uudistest, kohtulahenditest ning seadusemuudatustest." />
    <meta name="keywords" content="elatis, elatise uudised, elatise arvutamine, el<PERSON><PERSON><PERSON>, lapsetoetus, Riigikohtu lahend, elatis 2025" />
    <meta name="author" content="Alimendid.ee">

    <meta property="og:title" content="Elatise uudised juunis" />
    <meta property="og:url" content="https://alimendid.ee/et/blogi/2025/elatise-uudised-juunis.php" />
    <meta property="og:image" content="https://alimendid.ee/assets/failid/pildid/blogi/2025/elatise-uudised-juunis.jpg" />
    <meta property="og:image:alt" content="Elatise uudised juunis" />
    <meta property="og:description" content="Ülevaade juunis 2025 avaldatud elatise ja peretoetustega seotud uudistest, kohtulahenditest ning seadusemuudatustest." />
    <meta property="og:type" content="website" />
    <meta property="og:locale" content="et_ee" />
    <meta property='og:site_name' content='Alimendid.ee' />
    <meta property='article:author' content='https://www.facebook.com/Alimendid.ee' />
    <meta property='article:publisher' content='https://www.facebook.com/Alimendid.ee' />
    <meta property='article:published_time' content='2025-06-30' />

    <!-- Title -->
    <title>Elatise uudised juunis ‧ Alimendid.ee</title>


    <!-- Favicon -->
    <link rel="shortcut icon" href="../../../assets/failid/favicon/favicon.ico">

    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- CSS -->
    <link rel="stylesheet" href="../../../assets/css/main.min.css">
    <link href="../../../output.css" rel="stylesheet" />

    <!-- Theme Check and Update -->
    <script>
        const html = document.querySelector('html');
        const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
        const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

        if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
        else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
        else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
        else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
    </script>

    <!-- Google Ads-->
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'UA-221277240-1');
    </script>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-C8JJCED3EQ"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'G-C8JJCED3EQ');
    </script>

    <!-- Google Tag Manager -->
    <script>
        (function(w, d, s, l, i) {
            w[l] = w[l] || [];
            w[l].push({
                'gtm.start': new Date().getTime(),
                event: 'gtm.js'
            });
            var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s),
                dl = l != 'dataLayer' ? '&l=' + l : '';
            j.async = true;
            j.src =
                'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
            f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-T554HTP');
    </script>

    <!-- Hotjar -->
    <script>
        (function(h, o, t, j, a, r) {
            h.hj = h.hj || function() {
                (h.hj.q = h.hj.q || []).push(arguments)
            };
            h._hjSettings = {
                hjid: 3283746,
                hjsv: 6
            };
            a = o.getElementsByTagName('head')[0];
            r = o.createElement('script');
            r.async = 1;
            r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
            a.appendChild(r);
        })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
    </script>
</head>

<body class="dark:bg-neutral-900">
    <!-- Google Tag Manager -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T554HTP" height="0" width="0"
            style="display:none;visibility:hidden"></iframe></noscript>

    <!-- ========== HEADER ========== -->
    <?php include '../../../assets/failid/komponendid/et/menu.php'; ?>
    <!-- ========== END HEADER ========== -->

    <!-- ========== MAIN CONTENT ========== -->
    <main id="content">

        <div class="max-w-3xl px-4 pt-6 lg:pt-10 pb-12 sm:px-6 lg:px-8 mx-auto">
            <img class="w-full object-cover rounded-xl mb-8" src="../../../assets/failid/pildid/blogi/2025/elatise-uudised-juunis.jpg" alt="Elatise uudised juunis">
            </h2>

            <!-- //LINK ‧ Pealkiri -->
            <h1 class="text-4xl lg:text-5xl md:text-5xl sm:text-4xl font-semibold mb-6 postitus-pealkiri">Elatise uudised juunis</h1>

            <div class="flex items-center gap-4 mb-6">
                <img class="size-12 rounded-full" src="../../../assets/failid/pildid/autor/alimendid.jpg" alt="Alimendid.ee">
                <!-- //LINK ‧ Kuupäev -->
                <div>
                    <div class="font-semibold text-gray-800 postitus-autor">Alimendid.ee</div>
                    <span class="text-sm text-gray-500 postitus-kuupaev">30.05.2025</span>
                    <span class="text-sm text-gray-500 postitus-middledot"> · </span>
                    <span class="text-sm text-gray-500 postitus-lugemine">2 minuti lugemine</span>
                </div>
            </div>

            <!-- //LINK ‧ Uudised -->
            <!-- Uudise algus -->
            <div class="news-item pb-4">
                <h3 class="text-xl font-semibold text-gray-800 uudis-pealkiri">
                    <a href="https://www.juristaitab.ee/et/elatisabi-noue" target="_blank" style="transition: color 0.3s ease; color: inherit;" onmouseover="this.style.color='#ff6c00'" onmouseout="this.style.color='inherit'">
                        Elatisabi nõue
                    </a>
                </h3>
                <div class="mb-2 text-sm text-gray-500 pt-2">
                    <span class="text-md uudis-kuupaev">04.06.2025</span>
                    <span class="text-md uudis-middledot"> · </span>
                    <span class="text-md uudis-allikas">juristaitab.ee</span>
                </div>
                <p class="text-lg text-gray-700 pb-3 uudis-tekst">Elatisabi nõue tekib siis, kui elatise saaja avalduse alusel algatatakse elatise sissenõudmiseks täitemenetlus ning kohtutäitur edastab Sotsiaalkindlustusametile elatise saaja taotluse täitemenetlusagse elatisabi saamiseks. Seejärel maksab Sotsiaalkindlustusamet elatise saajale täitemenetlusaegset elatisabi. Sotsiaalkindlustusameti poolt täitemenetlusaegse elatisabina väljamakstud summad nõuab Maksu- ja Tolliamet kohtutäituri kaudu sisse elatise täitemenetluses ehk täitemenetlusaegset elatisabi nõuet ja elatist nõutakse sisse ühes täitemenetluses.</p>
            </div>
            <!-- Uudise lõpp -->

            <!-- Uudise algus -->
            <div class="news-item pb-4">
                <h3 class="text-xl font-semibold text-gray-800 uudis-pealkiri">
                    <a href="https://www.sotsiaalkindlustusamet.ee/uudised/lasteabiee-lehelt-saab-infot-kuidas-toime-tulla-seksuaalse-vaarkohtlemisega-internetis" target="_blank" style="transition: color 0.3s ease; color: inherit;" onmouseover="this.style.color='#ff6c00'" onmouseout="this.style.color='inherit'">
                        Lasteabi.ee
                    </a>
                </h3>
                <div class="mb-2 text-sm text-gray-500 pt-2">
                    <span class="text-md uudis-kuupaev">05.06.2025</span>
                    <span class="text-md uudis-middledot"> · </span>
                    <span class="text-md uudis-allikas">sotsiaalkindlustusamet.ee</span>
                </div>
                <p class="text-lg text-gray-700 pb-3 uudis-tekst">Lasteabi.ee lehelt leiab lastele ja lapsevanematele suunatud materjalid seksuaalse väärkohtlemise kohta internetis. Infot saab selle kohta, kuidas väärkohtlemist ära tunda ja kuhu abi saamiseks pöörduda.</p>
            </div>
            <!-- Uudise lõpp -->

            <!-- Uudise algus -->
            <div class="news-item pb-4">
                <h3 class="text-xl font-semibold text-gray-800 uudis-pealkiri">
                    <a href="https://www.riigiteataja.ee/kohtulahendid/fail.html?id=410206350" target="_blank" style="transition: color 0.3s ease; color: inherit;" onmouseover="this.style.color='#ff6c00'" onmouseout="this.style.color='inherit'">
                        Riigikohtu otsus tsiviilasjas nr 2-23-8691/35
                    </a>
                </h3>
                <div class="mb-2 text-sm text-gray-500 pt-2">
                    <span class="text-md uudis-kuupaev">13.06.2025</span>
                    <span class="text-md uudis-middledot"> · </span>
                    <span class="text-md uudis-allikas">riigiteataja.ee</span>
                </div>
                <p class="text-lg text-gray-700 pb-3 uudis-tekst">Riigikohus täpsustas, kuidas elatist arvutada ja vähendada. Kohus rõhutas, et elatis ei tohi olla väiksem seadusega ette nähtud miinimumist ning arvutamisel arvestatakse baassummat, keskmise palga protsenti ja toetuste mahaarvamist. Kui laps viibib lahuselava vanema juures keskmiselt 7–15 ööpäeva kuus, tuleb elatist vähendada proportsionaalselt koosveedetud ajaga. Suhtluskorda hinnates lähtutakse kehtivast kohtulahendist või vanemate kokkuleppest, kuid seda saab vajaduse korral esitatud tõenditega ümber lükata. Kui laps elab püsivalt elatist maksva vanema juures, tuleb lähtuda tegelikust elukorraldusest.</p>
            </div>
            <!-- Uudise lõpp -->

            <!-- Uudise algus -->
            <div class="news-item pb-4">
                <h3 class="text-xl font-semibold text-gray-800 uudis-pealkiri">
                    <a href="https://sotsiaalkindlustusamet.ee/uudised/mida-teada-lapsetoetuse-kohta-parast-kooli-loppu" target="_blank" style="transition: color 0.3s ease; color: inherit;" onmouseover="this.style.color='#ff6c00'" onmouseout="this.style.color='inherit'">
                        Mida teada lapsetoetuse kohta pärast kooli lõppu?
                    </a>
                </h3>
                <div class="mb-2 text-sm text-gray-500 pt-2">
                    <span class="text-md uudis-kuupaev">13.06.2025</span>
                    <span class="text-md uudis-middledot"> · </span>
                    <span class="text-md uudis-allikas">sotsiaalkindlustusamet.ee</span>
                </div>
                <p class="text-lg text-gray-700 pb-3 uudis-tekst">Lapsetoetust makstakse üldjuhul kuni lapse 19-aastaseks saamiseni. Kui laps saab 19-aastaseks, kuid tal ei ole veel keskharidust, jätkatakse toetuse maksmist kuni selle õppeaasta lõpuni, mille jooksul ta saab 19-aastaseks. Kui laps lõpetab gümnaasiumi ja on 19-aastaseks saanud enne juunit, makstakse toetust kuni kooli lõpetamiseni. Viimane toetus juunikuu eest makstakse välja 6. juunil 2025. Kui aga kooli lõpetamise ajal ei ole laps veel 19-aastane, makstakse toetust kuni tema 19-aastaseks saamiseni.</p>
            </div>
            <!-- Uudise lõpp -->

            <!-- Uudise algus -->
            <div class="news-item pb-4">
                <h3 class="text-xl font-semibold text-gray-800 uudis-pealkiri">
                    <a href="https://www.juristaitab.ee/et/taxonomy/term/28?page=1" target="_blank" style="transition: color 0.3s ease; color: inherit;" onmouseover="this.style.color='#ff6c00'" onmouseout="this.style.color='inherit'">
                        Elatise tasumine kokkuleppel
                    </a>
                </h3>
                <div class="mb-2 text-sm text-gray-500 pt-2">
                    <span class="text-md uudis-kuupaev">16.06.2025</span>
                    <span class="text-md uudis-middledot"> · </span>
                    <span class="text-md uudis-allikas">juristaitab.ee</span>
                </div>
                <p class="text-lg text-gray-700 pb-3 uudis-tekst">Kui elatist makstakse notariaalse kokkuleppe alusel, ei saa seda kohtus vähendada. Olukorras, kus vanemad on leppinud kokku lapsele makstava elatise suuruses ja elatise suurust mõjutavad asjaolud on oluliselt muutunud, võib see anda alust öelda ülalpidamiskokkulepe kui kestvusleping VÕS § 196 lg 1 alusel erakorraliselt üles mõjuval põhjusel, eelkõige kui ülesütlevalt lepingupoolelt ei või kõiki asjaolusid ja mõlemapoolset huvi arvestades mõistlikult nõuda lepingu jätkamist kuni kokkulepitud tähtpäevani või etteteatamistähtaja lõppemiseni. Ülalpidamiskokkuleppe ülesütlemiseks võib anda alust mh asjaolu, et kohustatud vanemalt ülalpidamist saama õigustatud isikute ring on muutunud või kohustatud vanema sissetulek on oluliselt vähenenud või õigustatud vanema oma suurenenud. </p>
            </div>
            <!-- Uudise lõpp -->

            <!-- Uudise algus -->
            <div class="news-item pb-4">
                <h3 class="text-xl font-semibold text-gray-800 uudis-pealkiri">
                    <a href="https://www.juristaitab.ee/et/elatis-lapsele-8" target="_blank" style="transition: color 0.3s ease; color: inherit;" onmouseover="this.style.color='#ff6c00'" onmouseout="this.style.color='inherit'">
                        Elatis lapsele
                    </a>
                </h3>
                <div class="mb-2 text-sm text-gray-500 pt-2">
                    <span class="text-md uudis-kuupaev">27.06.2025</span>
                    <span class="text-md uudis-middledot"> · </span>
                    <span class="text-md uudis-allikas">juristaitab.ee</span>
                </div>
                <p class="text-lg text-gray-700 pb-3 uudis-tekst">Kui elatist nõutakse seadusjärgses [miinimum]määras, ei ole vaja lapse vajadusi tõendada vaid vajalik on need lahti kirjutada. See tähendab, et [hagi]avalduses kirjutatakse kui palju lapsele mille jaoks vaja on, ent dokumente vajaduste tõendamiseks esitada ei ole vaja. Kehtiva seadusjärgse elatise summa saate välja arvutada elatiskalkulaatori abil. Kui laps viibib koos isaga keskmiselt vähem kui 7 ööpäeva kuus, on kehtiv elatise seadusjärgne määr 301,74 eurot. Kui aga soovite elatist suuremas määras, tuleb lapse vajadusi mitte ainult kirjeldada, vaid esitada ka tõendid.</p>
            </div>
            <!-- Uudise lõpp -->

            <!-- Uudise algus -->
            <div class="news-item pb-4">
                <h3 class="text-xl font-semibold text-gray-800 uudis-pealkiri">
                    <a href="https://www.oiguskantsler.ee/sites/default/files/2025-06/Arvamus%20pohiseaduslikkuse%20jarelevalve%20asjas%20nr%205-25-4.pdf" target="_blank" style="transition: color 0.3s ease; color: inherit;" onmouseover="this.style.color='#ff6c00'" onmouseout="this.style.color='inherit'">
                        Perehüvitiste ja teenuste tervikanalüüs: uuringuteülene kokkuvõte ja tulemuste arutelu

                    </a>
                </h3>
                <div class="mb-2 text-sm text-gray-500 pt-2">
                    <span class="text-md uudis-kuupaev">27.06.2025</span>
                    <span class="text-md uudis-middledot"> · </span>
                    <span class="text-md uudis-allikas">oiguskantsler.ee</span>
                </div>
                <p class="text-lg text-gray-700 pb-3 uudis-tekst">Õiguspärast ootust lasterikka pere toetuse uuesti saamiseks ei olnud kaebajal tekkinud. Õiguspärane ootus oleks kaebajal saanud tekkida siis, kui jõustunud seaduse alusel oleks kaebajal tekkinud mõistlik ootus sellist toetust saada. Kaebajal ei saanud tekkida õiguspärast ootust toetuse väiksemas suuruses maksmise jätkamiseks, sest puudus seadus, mille alusel selline ootus oleks saanud tekkida. Kuna kaebajal ei saanud olla õiguspärast ootust lasterikka pere toetuse maksmise jätkamiseks, oli Riigikogu lasterikka pere toetuse maksmise tingimuste soodsamaks muutmisel vabam.</p>
            </div>
            <!-- Uudise lõpp -->

            <!-- Uudise algus -->
            <div class="news-item pb-4">
                <h3 class="text-xl font-semibold text-gray-800 uudis-pealkiri">
                    <a href="https://www.sm.ee/sites/default/files/documents/2025-06/Tervikanal%C3%BC%C3%BCsi%20teoreetilise%20osa%20kokkuv%C3%B5te.pdf?fbclid=IwY2xjawLaDMJleHRuA2FlbQIxMABicmlkETBLMWFzOXFvVUF4aXJZV0h5AR4O-whdfiXo__Vvg3KgWuFwNlIH6b4wjY_fPRuetNikOeQk6rg78tu3LF1ZJg_aem_fXKmMCec0Y58GnSqEliixg" target="_blank" style="transition: color 0.3s ease; color: inherit;" onmouseover="this.style.color='#ff6c00'" onmouseout="this.style.color='inherit'">
                        Perehüvitiste ja teenuste tervikanalüüs: uuringuteülene kokkuvõte ja tulemuste arutelu
                    </a>
                </h3>
                <div class="mb-2 text-sm text-gray-500 pt-2">
                    <span class="text-md uudis-kuupaev">30.06.2025</span>
                    <span class="text-md uudis-middledot"> · </span>
                    <span class="text-md uudis-allikas">sm.ee</span>
                </div>
                <p class="text-lg text-gray-700 pb-3 uudis-tekst">Uuring näitas, et lahuselavate vanemate puhul ei saa üksi last/lapsi kasvatavad vanemad 40% juhtudel teiselt vanemalt elatist ega ei ole välja nõudnud elatisabi. Samas on teada, et lapse saamise otsuseks on vaja turvatunnet, mille üheks osaks on ka kindlus, et raskuste korral pakub riik tuge. Meil on loodud riiklik elatisaabi, kuid paljudel juhtudel jääb see vanemal saamata, sest ei pöörduta elatise nõudega kohtusse.</p>
            </div>
            <!-- Uudise lõpp -->

        </div>

        <!-- Banner -->
        <?php include '../../../assets/failid/komponendid/et/blogi.php'; ?>
    </main>
    <!-- ========== END MAIN CONTENT ========== -->

    <!-- Footer -->
    <?php include '../../../assets/failid/komponendid/et/footer.php'; ?>

    <!-- JS PLUGINS -->
    <!-- Required plugins -->
    <script src="../../../assets/vendor/lodash/lodash.min.js"></script>
    <script src="../../../assets/vendor/preline/dist/index.js?v=3.0.1"></script>
    <!-- Clipboard -->
    <script src="../../../assets/vendor/clipboard/dist/clipboard.min.js"></script>
    <script src="../../../assets/js/hs-copy-clipboard-helper.js"></script>

    <script>
        window.addEventListener('load', () => {
            (function() {
                const tabsId = 'hs-pro-hero-tabs';
                const tabs = HSTabs.getInstance(`#${tabsId}`, true);
                const scrollNav = HSScrollNav.getInstance('#hs-pro-hero-tabs-scroll', true);

                tabs.element.on('change', ({
                    el
                }) => {
                    scrollNav.element.centerElement(el);
                });

                window.addEventListener('resize', _.debounce(() => {
                    scrollNav.element.centerElement(tabs.element.current);
                }, 100));

                window.addEventListener('change.hs.tab', ({
                    detail
                }) => {
                    if (detail.payload.tabsId !== tabsId) return false;

                    const tabs = document.querySelector('#hs-pro-hero-tabs-scroll');

                    window.scrollTo({
                        top: tabs.offsetTop,
                        behavior: 'smooth'
                    });
                });
            })();
        });
    </script>

</body>

</html>